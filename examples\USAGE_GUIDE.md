# 桥梁地震响应分析 - 内存直接传递使用指南

## 快速开始

### 1. 基本使用（推荐）

```bash
# 使用默认的内存直接传递模式
python analyze_xuhui.py
```

这将：
- 启用内存直接传递模式
- 不保存临时文件
- 自动清理内存数据
- 提供最佳性能

### 2. 传统文件模式

```bash
# 使用传统文件传递模式
python analyze_xuhui.py --file-transfer
```

适用于：
- 需要调试OpenSees输出
- 对兼容性要求极高的环境
- 内存资源受限的情况

### 3. 调试模式

```bash
# 内存传递 + 保存临时文件
python analyze_xuhui.py --save-temp-files
```

适用于：
- 开发和调试阶段
- 需要检查中间结果
- 问题排查

## 编程接口

### 基本用法

```python
from analyze_xuhui import AnalysisConfig, run_analysis
from params import BridgeParams
import numpy as np

# 准备参数
params = BridgeParams("config.json")
ground_motion = np.array([...])  # 地震动数据

# 使用默认配置（内存传递）
pier_data, bearing_data, stats = run_analysis(
    params, ground_motion, dt=0.02, pga=0.15
)
```

### 自定义配置

```python
# 高性能模式
config = AnalysisConfig(
    use_memory_transfer=True,
    save_temp_files=False,
    cleanup_temp_files=True
)

# 调试模式
config = AnalysisConfig(
    use_memory_transfer=True,
    save_temp_files=True,
    cleanup_temp_files=False
)

# 兼容模式
config = AnalysisConfig(
    use_memory_transfer=False,
    save_temp_files=True,
    cleanup_temp_files=True
)

# 执行分析
pier_data, bearing_data, stats = run_analysis(
    params, ground_motion, dt=0.02, pga=0.15, config=config
)
```

### 批量分析示例

```python
import numpy as np
from analyze_xuhui import AnalysisConfig, run_analysis
from params import BridgeParams

# 配置
params = BridgeParams("config.json")
config = AnalysisConfig(use_memory_transfer=True, save_temp_files=False)

# 批量处理多个地震动
ground_motions = np.load("ground_motions.npy")  # shape: (n_motions, n_steps)
results = []

for i, gm in enumerate(ground_motions):
    print(f"处理地震动 {i+1}/{len(ground_motions)}")
    pier_data, bearing_data, stats = run_analysis(
        params, gm, dt=0.02, pga=0.15, config=config
    )
    results.append((pier_data, bearing_data, stats))

print(f"批量分析完成，共处理 {len(results)} 个地震动")
```

## 配置选项详解

### AnalysisConfig 参数

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `use_memory_transfer` | bool | True | 启用内存直接传递模式 |
| `save_temp_files` | bool | False | 是否保存临时文件到磁盘 |
| `temp_file_dir` | str | "temp_results" | 临时文件存储目录 |
| `cleanup_temp_files` | bool | True | 分析完成后是否清理临时文件 |

### 模式选择建议

#### 内存传递模式（推荐）
```python
config = AnalysisConfig(use_memory_transfer=True, save_temp_files=False)
```
**优点**：
- 减少磁盘IO操作
- 提高并发性能
- 简化文件管理
- 适合批量分析

**适用场景**：
- 生产环境
- 大批量分析
- 云计算环境
- 内存充足的系统

#### 文件传递模式
```python
config = AnalysisConfig(use_memory_transfer=False, save_temp_files=True)
```
**优点**：
- 完全兼容原有系统
- 可以检查中间文件
- 内存使用较少

**适用场景**：
- 调试和开发
- 内存受限环境
- 需要检查OpenSees输出

## 性能优化建议

### 1. 系统配置
- **内存**：建议8GB以上，大批量分析需要16GB+
- **CPU**：多核处理器，OpenSees支持并行计算
- **存储**：SSD硬盘可提升文件模式性能

### 2. 分析参数
- **时间步长**：平衡精度和效率，通常0.01-0.02s
- **地震动长度**：根据需要选择，过长会增加计算时间
- **模型复杂度**：简化不必要的细节

### 3. 批量分析优化
```python
# 使用内存传递模式
config = AnalysisConfig(
    use_memory_transfer=True,
    save_temp_files=False,
    cleanup_temp_files=True
)

# 分批处理大量地震动
batch_size = 100
for i in range(0, len(ground_motions), batch_size):
    batch = ground_motions[i:i+batch_size]
    # 处理当前批次
    for gm in batch:
        result = run_analysis(params, gm, config=config)
        # 处理结果
```

## 故障排除

### 常见问题

#### 1. 内存不足
**症状**：程序崩溃或系统变慢
**解决方案**：
- 切换到文件传递模式
- 减少并发分析数量
- 增加系统内存

#### 2. 数据不一致
**症状**：两种模式结果差异大
**解决方案**：
- 检查OpenSees版本
- 验证模型配置
- 运行测试验证脚本

#### 3. 性能未提升
**症状**：内存模式不比文件模式快
**解决方案**：
- 检查磁盘性能（SSD vs HDD）
- 增加分析规模（批量分析）
- 检查系统负载

### 调试工具

#### 运行测试
```bash
# 基本功能测试
python test_memory_transfer.py

# 性能对比测试
python test_memory_transfer.py --compare

# 使用示例
python example_usage.py
```

#### 检查配置
```python
from analyze_xuhui import AnalysisConfig

config = AnalysisConfig()
print(f"内存传递: {config.use_memory_transfer}")
print(f"保存临时文件: {config.save_temp_files}")
print(f"清理临时文件: {config.cleanup_temp_files}")
```

## 最佳实践

### 1. 生产环境
```python
# 推荐配置
config = AnalysisConfig(
    use_memory_transfer=True,
    save_temp_files=False,
    cleanup_temp_files=True
)
```

### 2. 开发环境
```python
# 调试配置
config = AnalysisConfig(
    use_memory_transfer=True,
    save_temp_files=True,
    cleanup_temp_files=False
)
```

### 3. 兼容环境
```python
# 保守配置
config = AnalysisConfig(
    use_memory_transfer=False,
    save_temp_files=True,
    cleanup_temp_files=True
)
```

## 更新和维护

### 版本兼容性
- 向后兼容所有现有代码
- 默认启用内存传递模式
- 可随时切换到文件模式

### 未来改进
- 支持更多数据格式
- 优化内存使用
- 增强并行处理能力
- 添加更多配置选项

---

**注意**：首次使用建议先运行测试脚本验证功能正常，然后根据具体需求选择合适的配置模式。
