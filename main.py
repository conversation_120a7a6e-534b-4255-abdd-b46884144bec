import openseespy.opensees as ops
import opstool as opst
import opstool.vis.plotly as opsvis
import matplotlib.pyplot as plt

from params import BridgeParams
from core.simply_supported_beam_model import SimplySupportedBeamModel
from analysis.analyzer import BridgeAnalyzer
from utils.mass_analysis import analyze_and_report


# 1. Create bridge model
config_file = "E:/Codes/opensees/徐汇区桥梁群/data/configs/bridge-65-龙吴路龙华港中桥.json"
# params = BridgeParams(config_file)
params = BridgeParams()
model = SimplySupportedBeamModel(params)
# results = analyze_and_report(model, plot=True)

# Visualize
fig = model.visualize()
fig.write_html("bridge_model.html")

# 2. Perform analysis
analysis = BridgeAnalyzer(model)

# 2.1 Modal analysis
num_modes = 3
analysis.modal(num_modes)
opst.post.save_eigen_data(odb_tag=2, mode_tag=num_modes)

# Visualize modes
fig = opsvis.plot_eigen(mode_tags=num_modes, odb_tag=2, subplots=False, show_outline=False)
fig.write_html("bridge_modes.html")
fig.show()

# 2.2 Dynamic analysis - disabled for simply supported model
analysis.dynamic(pga=0.5)

