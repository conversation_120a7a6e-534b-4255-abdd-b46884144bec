"""
Bridge Bearing Response Recorder Module

This module provides functions for recording time history responses of bridge bearings
during dynamic analysis using OpenSees recorders.

The module records:
- Bearing element forces in X, Y, Z directions
- Bearing element deformations
"""

import os
import openseespy.opensees as ops


def find_bearings_closest_to_y0(model):
    """
    Find representative bearings for recording.
    For abutments: select the bearing closest to y=0 at each x-coordinate.
    For cap beams: select two bearings closest to y=0 (one for each span) at each x-coordinate.

    Args:
        model: Bridge model object

    Returns:
        list: List of bearing indices that are representative for recording
    """
    # Check if there are bearings in the model
    if not model.bearings['elements'] or not model.bearings['connections']:
        return []

    # Dictionary to store bearings by x-coordinate and span
    bearings_by_x_span = {}

    # Group bearings by x-coordinate and span
    for i, (deck_node, support_node) in enumerate(model.bearings['connections']):
        # Get bearing coordinates from deck node
        x_coord = ops.nodeCoord(deck_node, 1)
        y_coord = ops.nodeCoord(deck_node, 2)
        span_num = model.bearings['spans'][i]

        # Round x-coordinate to avoid floating point issues
        x_coord_rounded = round(x_coord, 3)

        # Create nested dictionary structure
        if x_coord_rounded not in bearings_by_x_span:
            bearings_by_x_span[x_coord_rounded] = {}

        if span_num not in bearings_by_x_span[x_coord_rounded]:
            bearings_by_x_span[x_coord_rounded][span_num] = []

        bearings_by_x_span[x_coord_rounded][span_num].append((i, y_coord, deck_node, support_node))

    # Find representative bearings at each x-coordinate
    selected_bearing_indices = []

    for x_coord, spans_dict in bearings_by_x_span.items():
        # For each span at this x-coordinate, select the bearing closest to y=0
        for span_num, bearings in spans_dict.items():
            # Sort bearings by absolute y-coordinate
            bearings.sort(key=lambda b: abs(b[1]))

            # Get the bearing closest to y=0 for this span
            closest_bearing_idx = bearings[0][0]
            selected_bearing_indices.append(closest_bearing_idx)

    return selected_bearing_indices


def _setup_bearing_recorder(model, bearing_elem, bearing_idx, dt, output_dir, recorder_info):
    """
    Set up OpenSees recorders for a single bearing element.

    Args:
        model: Bridge model object
        bearing_elem: Bearing element tag
        bearing_idx: Bearing index
        dt: Time step for recording
        output_dir: Directory to store recorder output files
        recorder_info: Dictionary to store recorder information

    Returns:
        dict: Updated recorder information
    """
    # Get bearing connection nodes
    deck_node, support_node = model.bearings['connections'][bearing_idx]

    # Get bearing coordinates
    x_coord = ops.nodeCoord(deck_node, 1)
    y_coord = ops.nodeCoord(deck_node, 2)
    z_coord = ops.nodeCoord(deck_node, 3)

    # Create file names
    bearing_id = f"bearing_{bearing_idx}_x{x_coord:.1f}_y{y_coord:.1f}"
    force_file = f"{output_dir}/bearing_force_{bearing_id}.txt"
    deform_file = f"{output_dir}/bearing_deform_{bearing_id}.txt"

    # Set up force recorder for bearing element
    # For zeroLength elements, forces are [Fx, Fy, Fz, Mx, My, Mz]
    force_recorder = ops.recorder('Element', '-file', force_file, '-time', '-dT', dt,
                                '-ele', bearing_elem, 'force')

    # Set up deformation recorder for bearing element
    # For zeroLength elements, deformations are [dx, dy, dz, rx, ry, rz]
    deform_recorder = ops.recorder('Element', '-file', deform_file, '-time', '-dT', dt,
                                    '-ele', bearing_elem, 'deformation')

    # Store recorder information
    recorder_info['bearings'][bearing_id] = {
        'bearing_idx': bearing_idx,
        'elem_tag': bearing_elem,
        'deck_node': deck_node,
        'support_node': support_node,
        'x_coord': x_coord,
        'y_coord': y_coord,
        'z_coord': z_coord,
        'force_file': force_file,
        'deform_file': deform_file
    }

    recorder_info['recorder_tags'].extend([force_recorder, deform_recorder])

    return recorder_info


def setup_bearing_recorders(model, dt, output_dir='results', recorder_info=None):
    """
    Set up OpenSees recorders for representative bearings.
    For abutments: records the bearing closest to y=0 at each x-coordinate.
    For cap beams: records two bearings closest to y=0 (one for each span) at each x-coordinate.
    This reduces the number of recorded bearings while maintaining representative data.

    Args:
        model: Bridge model object
        dt: Time step for recording
        output_dir: Directory to store recorder output files
        recorder_info: Existing recorder information dictionary to update

    Returns:
        dict: Dictionary with recorder information
    """
    # Create output directory if it doesn't exist
    os.makedirs(output_dir, exist_ok=True)

    # Initialize recorder info if not provided
    if recorder_info is None:
        recorder_info = {
            'bearings': {},
            'recorder_tags': []
        }
    elif 'bearings' not in recorder_info:
        recorder_info['bearings'] = {}

    # Check if there are bearings in the model
    if not model.bearings['elements']:
        print("警告: 模型中没有支座单元，无法设置支座响应记录器")
        return recorder_info

    # Find bearings closest to y=0 at each x-coordinate
    selected_bearing_indices = find_bearings_closest_to_y0(model)

    if not selected_bearing_indices:
        print("警告: 未找到符合条件的支座，无法设置支座响应记录器")
        return recorder_info

    print(f"选择代表性支座 ({len(selected_bearing_indices)} in {len(model.bearings['elements'])}):")

    # Set up recorders for selected bearings only
    initial_recorder_count = len(recorder_info['recorder_tags'])

    for bearing_idx in selected_bearing_indices:
        bearing_elem = model.bearings['elements'][bearing_idx]
        try:
            recorder_info = _setup_bearing_recorder(
                model, bearing_elem, bearing_idx, dt, output_dir, recorder_info
            )

            # Get bearing coordinates and span info for logging
            deck_node, _ = model.bearings['connections'][bearing_idx]
            x_coord = ops.nodeCoord(deck_node, 1)
            y_coord = ops.nodeCoord(deck_node, 2)
            span_num = model.bearings['spans'][bearing_idx]
            print(f"  - 支座 {bearing_idx}:\tx={x_coord:.1f},\ty={y_coord:.1f},\t跨号={span_num}")

        except Exception as e:
            print(f"设置支座 {bearing_idx} (元素 {bearing_elem}) 记录器时出错: {e}")

    # Log both recorder instances and bearing count (Plan A)
    added_recorder_instances = len(recorder_info['recorder_tags']) - initial_recorder_count
    bearing_count = len(recorder_info['bearings'])
    print(f"已设置 {added_recorder_instances} 个支座响应记录器实例（{bearing_count} 个支座，每个 2 个）")
    return recorder_info


def read_bearing_force_data(force_file):
    """
    Read bearing force data from recorder output file.

    Args:
        force_file: Path to bearing force file

    Returns:
        dict: Dictionary containing time and force data
    """
    if not os.path.exists(force_file):
        print(f"警告: 支座力文件 {force_file} 不存在")
        return {}

    try:
        import numpy as np
        data = np.loadtxt(force_file)

        if data.size == 0:
            print(f"警告: 支座力文件 {force_file} 为空")
            return {}

        if data.ndim == 1:
            data = data.reshape(1, -1)

        # For zeroLength elements: [time, Fx, Fy, Fz, Mx, My, Mz]
        force_data = {
            'time': data[:, 0],
            'force_x': data[:, 1],  # X方向力 (N)
            'force_y': data[:, 2],  # Y方向力 (N)
            'force_z': data[:, 3],  # Z方向力 (N)
            'moment_x': data[:, 4], # X方向弯矩 (N·m)
            'moment_y': data[:, 5], # Y方向弯矩 (N·m)
            'moment_z': data[:, 6]  # Z方向弯矩 (N·m)
        }
        return force_data

    except Exception as e:
        print(f"读取支座力文件 {force_file} 时出错: {e}")
        return {}


def read_bearing_deformation_data(deform_file):
    """
    Read bearing deformation data from recorder output file.

    Args:
        deform_file: Path to bearing deformation file

    Returns:
        dict: Dictionary containing time and deformation data
    """
    if not os.path.exists(deform_file):
        print(f"警告: 支座变形文件 {deform_file} 不存在")
        return {}

    try:
        import numpy as np
        data = np.loadtxt(deform_file)

        if data.size == 0:
            print(f"警告: 支座变形文件 {deform_file} 为空")
            return {}

        if data.ndim == 1:
            data = data.reshape(1, -1)

        # For zeroLength elements: [time, dx, dy, dz, rx, ry, rz]
        deform_data = {
            'time': data[:, 0],
            'deform_x': data[:, 1],  # X方向变形 (m)
            'deform_y': data[:, 2],  # Y方向变形 (m)
            'deform_z': data[:, 3],  # Z方向变形 (m)
            'rotation_x': data[:, 4], # X方向转角 (rad)
            'rotation_y': data[:, 5], # Y方向转角 (rad)
            'rotation_z': data[:, 6]  # Z方向转角 (rad)
        }
        return deform_data

    except Exception as e:
        print(f"读取支座变形文件 {deform_file} 时出错: {e}")
        return {}


def get_bearing_hysteresis_data(bearing_id, results_dir='results'):
    """
    Get bearing hysteresis data (force vs deformation) for plotting.

    Args:
        bearing_id: Bearing identifier (e.g., 'bearing_0_x15.0_y0.0')
        results_dir: Directory containing recorder output files

    Returns:
        dict: Dictionary containing hysteresis data
    """
    force_file = os.path.join(results_dir, f'bearing_force_{bearing_id}.txt')
    deform_file = os.path.join(results_dir, f'bearing_deform_{bearing_id}.txt')

    force_data = read_bearing_force_data(force_file)
    deform_data = read_bearing_deformation_data(deform_file)

    if not force_data or not deform_data:
        return {}

    # Calculate horizontal force and deformation
    import numpy as np
    force_h = np.sqrt(force_data['force_x']**2 + force_data['force_y']**2)
    deform_h = np.sqrt(deform_data['deform_x']**2 + deform_data['deform_y']**2)

    hysteresis_data = {
        'time': force_data['time'],
        'force_x': force_data['force_x'],
        'force_y': force_data['force_y'],
        'force_z': force_data['force_z'],
        'force_h': force_h,  # 水平合力
        'deform_x': deform_data['deform_x'],
        'deform_y': deform_data['deform_y'],
        'deform_z': deform_data['deform_z'],
        'deform_h': deform_h  # 水平变形合成
    }

    return hysteresis_data
