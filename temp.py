#%%
import numpy as np
import matplotlib.pyplot as plt

d0 = np.load('out/bridge-53-漕宝路蒲汇塘桥.npz')
d1 = np.load('out/bridge-53-漕宝路蒲汇塘桥_20_27.npz')

# plt.plot(d0['pier_displacements'][0,10], linewidth=1)
# plt.plot(d1['pier_displacements'][0,0], linewidth=.75)
# plt.plot([0,15000], [0,0], linewidth=1)

# print(list(d0.keys()))
# print(list(d1.keys()))

print(d0['pier_displacements'].shape)
print(d1['pier_displacements'].shape)

pier_displacements = np.concatenate([
    d0['pier_displacements'][:, :20], d1['pier_displacements'],
], 1)
bearing_displacements = np.concatenate([
    d0['bearing_displacements'][:, :20], d1['bearing_displacements'],
], 1)
bearing_forces = np.concatenate([
    d0['bearing_forces'][:, :20], d1['bearing_forces'],
], 1)

# d0['pier_displacements'].mean((0,2))

assert d0['pier_keys'].any() == d1['pier_keys'].any()
assert d0['bearing_indices'].any() == d1['bearing_indices'].any()
assert d0['is_single_span'] == d1['is_single_span']
# del d0, d1

#%%
output_file = 'out/bridge-53-漕宝路蒲汇塘桥.npz'

np.savez_compressed(
    output_file,
    pier_displacements=pier_displacements,
    bearing_displacements=bearing_displacements,
    bearing_forces=bearing_forces,
    pier_keys=d0['pier_keys'],
    bearing_indices=d0['bearing_indices'],
    is_single_span=d0['is_single_span']
)

# %%
import os

for fn in os.listdir('out/'):
    if fn[-3:] == 'npz':
        d0 = np.load('out/' + fn)
        print(d0['pier_displacements'].shape, fn)
        if not d0['is_single_span']:
            assert d0['pier_displacements'].mean((0,2)).any() != 0
        del d0
# %%
