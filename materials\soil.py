import openseespy.opensees as ops


# 桥台填土只受压非线性弹簧材料
def set_backfill(params, mat_tags):
    '''
    创建桥台填土只受压非线性弹簧材料
    -------------------------------------
    E  : 压缩模量 (N/m), 上海浅部素填土, 取50MPa[1]
    fy : 屈服强度 (N/m2), 取70KPa[2]
    gap: 间隙大小 (m)[2]
    -------------------------------------
    [1] 软土压缩模量对基坑支护结构力学性能影响研究
    [2] fy, gap取负值

    左右桥台的填土材料方向不同
    假定桥台填土深度2.5m, 宽度=桥面宽度+1.5*2m
    
    20250510修改:
    参考强震作用下桥台对斜交连续梁桥抗震性能的影响研究
    E = 0.5 * 421776kN/m (黏性土)
    -------------------------------------
    20250815修改:
    依据 Caltrans Seismic Design Criteria Version 2.1 (*******节)
    桥台宽度 w_abut = params.deck_width (ft)
    背墙高度 h_abut = h_bw (2ft-10ft)
    极限被动承载力 F_abut = w_abut * 5.5*power(h_abut, 2.5) / (1+2.37*h_abut) (kip)
    桥台纵向刚度 K_abut = w_abut * (5.5*h_abut + 20) (kip/in)
    有效桥台刚度 K_eff = F_abut / (Δ_gap + F_abut/K_abut) (k/in)
    '''
    FT_TO_M = 0.3048
    KIP_TO_N = 4448.22
    IN_TO_M = 0.0254
    KIP_PER_IN_TO_N_PER_M = KIP_TO_N / IN_TO_M
    
    w_abut_ft = params.deck_width / FT_TO_M
    h_bw_ft = params.abutment['backwall_height'] / FT_TO_M
    
    F_abut_kip = w_abut_ft * 5.5 * (h_bw_ft ** 2.5) / (1 + 2.37 * h_bw_ft)  # kip
    K_abut_kip_per_in = w_abut_ft * (5.5 * h_bw_ft + 20)  # kip/in
    
    F_abut_N = F_abut_kip * KIP_TO_N
    K_abut_N_per_m = K_abut_kip_per_in * KIP_PER_IN_TO_N_PER_M
    # K_eff_N_per_m = F_abut_N / (params.abutment['gap'] + F_abut_N / K_abut_N_per_m)
    
    # # 桥台对连续梁桥纵向地震响应的影响 王翼, 李建中
    # E = params.abutment['backfill_E']
    # fy = params.abutment['backfill_fy']
    
    # 左桥台填土材料 - 只受压，正向位移时提供阻力
    ops.uniaxialMaterial('ElasticPPGap', mat_tags["BackfillLeft"], K_abut_N_per_m, F_abut_N, 0, 0)

    # 右桥台填土材料 - 只受压，负向位移时提供阻力
    # 注意：对于右桥台，当桥梁向右移动时，桥台应提供向左的阻力
    ops.uniaxialMaterial('ElasticPPGap', mat_tags["BackfillRight"], K_abut_N_per_m, -F_abut_N, 0, 0)


def set_pile_abutment(params, mat_tags):
    # 公路桥涵地基与基础设计规范 JTG 3363—2019 P89
    m = params.pile_soil["m_value"]
    d = params.abutment["pile_diameter"]
    n = params.abutment["pile_number"]
    b1 = 0.9 * (d + 1) if d >= 1.0 else 0.9 * (1.5*d + 0.5)
    
    Ec = 3e10 # C30混凝土弹性模量, Pa
    I = 3.1415927 * (d ** 4) / 64
    EI = 0.8 * Ec * I
    alpha = (m * b1 / EI) ** 0.2
    
    C = 1.0
    K_single_pile = (alpha ** 3) * EI / C
    
    group_effect_factor = params.pile_soil["group_effect_factor"]
    K_total = n * K_single_pile * group_effect_factor
    
    ops.uniaxialMaterial('Elastic', mat_tags["PileAbutment"], K_total)


def set_pile_pier(params, mat_tags):
    if params.num_piers_transverse == 0:
        return
    
    # 公路桥涵地基与基础设计规范 JTG 3363—2019 P89
    m = params.pile_soil["m_value"]
    d = params.pile_soil["pile_diameter_pier"]
    n = params.pile_soil["pile_number_pier"]
    b1 = 0.9 * (d + 1) if d >= 1.0 else 0.9 * (1.5*d + 0.5)
    
    Ec = 3e10 # C30混凝土弹性模量, Pa
    I = 3.1415927 * (d ** 4) / 64
    EI = 0.8 * Ec * I
    alpha = (m * b1 / EI) ** 0.2
    
    C = 1.0
    K_single_pile = (alpha ** 3) * EI / C
    
    group_effect_factor = params.pile_soil["group_effect_factor"]
    K_total = n * K_single_pile * group_effect_factor
    K_total /= params.num_piers_transverse
    
    ops.uniaxialMaterial('Elastic', mat_tags["PilePierColumn"], K_total)