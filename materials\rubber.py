import openseespy.opensees as ops


# 线性板式橡胶支座材料 (default)
def set_rubber(params, mat_tags, axial_loads=None):
    '''
    =====================================
    《CJJ 166-2011 城市桥梁抗震设计规范》
    -------------------------------------
    G: 动剪切模量 (N/m²), 默认1200e3
    A: 剪切面积 (m²)
    t: 橡胶层总厚度 (m)
    =====================================
    《JT/T4-2019 公路桥梁板式橡胶支座》
    -------------------------------------
    假定型号GBZJ300*400*35
    A = 0.3*0.4 = 0.12 m2
    t = 25 mm
    最大承压力 Rck = 361 KN
    接触摩擦系数 μ = 0.3(混凝土) / 0.2(钢板)
    抗压弹性模量 E = 5.4G x S^2
    =====================================
    参考：
    (周连绪等, 2024. 板式橡胶支座梁桥抗震韧性评估. 东南大学学报)
    GA/t ∈ [2613, 4480] kN/m, A=0.35^2 m2
    以普通板式橡胶支座梁桥为例,该类桥梁与橡胶类隔震支座(高阻尼橡胶文座或铅芯橡胶支座)
    或固定支座支承桥梁不同,几乎无复位能力。地震作用下主梁可滑动,桥梁下部结构鲜有损伤,
    主要震害为主梁移位或落座，本文采用支座残余位移(主梁与桥墩或主梁与桥台间的永久相对位移)
    表征主梁的移位,采用支座峰值位移来判定地震过程中主梁是否落座。

    参数:
        params: 桥梁参数
        mat_tags: 材料标签字典
        axial_loads: 支座轴向荷载字典 {elem_tag: axial_load}，用于计算屈服位移
    '''

    bearing = params.bearing[params.rubber]  # GBZJ200x200x35 / GBZJ300x350x52
    G = bearing['G']
    A = bearing['A']  # 每端两个支座
    t = bearing['te']
    S = bearing['s']
    E = 5.4 * G * S * S

    K1 = G * A / t  # 剪切刚度 N/m
    K2 = E * A / bearing['t']  # 抗压刚度
    
    if axial_loads is None:
        # 如果没有提供轴向荷载，使用弹性材料
        ops.uniaxialMaterial('Elastic', mat_tags["RubberX"], K1)  # RubberX 水平方向剪切刚度
        ops.uniaxialMaterial('Elastic', mat_tags["RubberZ"], K2)  # RubberZ 竖直方向抗压
        ops.uniaxialMaterial('Elastic', mat_tags["RubberRxy"], 1e12)  # RubberRxy 水平方向弯曲刚度
        ops.uniaxialMaterial('Elastic', mat_tags["RubberRz"], 1e12)   # RubberRz 水平方向转动刚度
    else:
        # 如果提供了轴向荷载，使用弹塑性材料模拟摩擦滑动
        # 摩擦系数 (参考JT/T4-2009)
        mu = params.bearing['friction_coef']  # 使用参数中定义的摩擦系数

        # 创建弹塑性材料，为每个支座单独创建材料
        for elem_tag, axial_load in axial_loads.items():
            # 计算屈服位移 = 摩擦力 / 水平刚度
            # 摩擦力 = 摩擦系数 * 轴向荷载
            friction_force = mu * abs(axial_load)
            yield_disp = friction_force / K1

            # 创建弹塑性材料
            # ElasticPP参数: tag, E, epsyP, epsyN, eps0
            mat_tag = mat_tags["RubberX"] + elem_tag + 1000  # 为每个支座创建唯一的材料标签
            ops.uniaxialMaterial('ElasticPP', mat_tag, K1, yield_disp, -yield_disp, 0.0)

            # 存储材料标签和屈服位移，用于后续参考
            if not hasattr(params, 'bearing_materials'):
                params.bearing_materials = {}
            params.bearing_materials[elem_tag] = {
                'mat_tag': mat_tag,
                'axial_load': axial_load,
                'friction_force': friction_force,
                'yield_disp': yield_disp
            }
            # print(params.bearing_materials[elem_tag])
            
