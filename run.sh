#!/bin/bash

#SBATCH -p v6_384
#SBATCH -N 1
#SBATCH -n 1

echo "Job started on $(date)"
echo "Job ID: $SLURM_JOB_ID"
echo "Job Name: $SLURM_JOB_NAME"
echo "Running on nodes: $SLURM_JOB_NODELIST"
echo ""

source /public5/soft/modules/module.sh
module load miniforge/24.11
module load gcc/12.2
export CC=gcc
export CXX=g++
# source activate opensees

cd ./opensees

python analyze_xuhui.py

echo "Job finished on $(date)"
