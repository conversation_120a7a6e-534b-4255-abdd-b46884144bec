import numpy as np


def calculate_target_curvature(params, target_strain=0.004):
    """
    预先计算截面边缘混凝土压应变达到目标值时的截面曲率
    考虑钢筋对中性轴位置和力平衡的影响

    参数:
        params: 桥梁参数对象
        target_strain: 目标压应变 (默认0.004)

    返回:
        float: 目标曲率 (1/m)
    """
    if params.num_spans == 1:
        return 0.0

    # 获取截面参数
    D = params.pier_section["diameter"]  # 桥墩直径 (m)
    cover = params.pier_section["concrete_cover"]  # 保护层厚度 (m)

    # 获取材料参数
    fc = params.concrete_materials["core"]["fc"] * 1e6  # 核心区混凝土强度 (Pa)
    fy = params.steel_materials["longitudinal"]["fy"] * 1e6  # 钢筋屈服强度 (Pa)
    Es = params.steel_materials["longitudinal"]["Es"] * 1e6  # 钢筋弹性模量 (Pa)

    # 获取钢筋参数
    rho = params.pier_section['longitudinal_bars']['rho']  # 配筋率
    bar_dia = params.pier_section['longitudinal_bars']['diameter']  # 钢筋直径

    # 获取轴力
    P = params.pier_section.get('axial_force', 0)  # 轴力 (N)

    # 截面几何参数
    A_gross = np.pi * (D/2)**2  # 毛截面面积 (m²)

    # 钢筋重心半径（假设均匀分布在圆周上）
    d = D/2 - cover - bar_dia/2  # 钢筋重心到截面中心的距离

    # 迭代求解中性轴深度，考虑钢筋作用
    c = solve_neutral_axis_with_steel(D, cover, fc, fy, Es, rho, P, target_strain, d)

    # 计算目标曲率
    curvature = target_strain / c

    # 验证钢筋应力状态
    steel_strain = (d - c) / c * target_strain  # 钢筋应变
    steel_stress = min(Es * abs(steel_strain), fy)  # 钢筋应力

    print(f"目标压应变 {target_strain:.4f} 对应的截面曲率: {curvature:.6f} (1/m)")
    print(f"  - 中性轴深度: {c:.3f} m")
    print(f"  - 钢筋应变: {steel_strain:.6f}")
    print(f"  - 钢筋应力: {steel_stress/1e6:.1f} MPa")
    print(f"  - 轴压比: {P/(A_gross*fc):.4f}")

    return curvature


def solve_neutral_axis_with_steel(D, cover, fc, fy, Es, rho, P, target_strain, d):
    """
    考虑钢筋作用的中性轴深度求解
    基于力平衡和应变协调条件

    参数:
        D: 截面直径 (m)
        cover: 保护层厚度 (m)
        fc: 混凝土强度 (Pa)
        fy: 钢筋屈服强度 (Pa)
        Es: 钢筋弹性模量 (Pa)
        rho: 配筋率
        P: 轴力 (N)
        target_strain: 目标压应变
        d: 钢筋重心半径 (m)

    返回:
        float: 中性轴深度 (m)
    """
    def force_equilibrium(c):
        """力平衡方程"""
        # 混凝土压应力合力（简化为矩形应力块）
        beta1 = 0.85 if fc/1e6 <= 28 else max(0.65, 0.85 - 0.05*(fc/1e6 - 28)/7)
        a = beta1 * c  # 等效矩形应力块高度

        # 计算混凝土压应力合力
        if a <= D:
            # 压应力块在截面内
            theta = 2 * np.arccos(max(-1, min(1, (D/2 - a) / (D/2))))  # 压应力块对应的圆心角
            A_comp = (D/2)**2 * (theta - np.sin(theta)) / 2  # 压应力块面积
            Cc = 0.85 * fc * A_comp  # 混凝土压力合力
        else:
            # 整个截面受压
            Cc = 0.85 * fc * np.pi * (D/2)**2

        # 钢筋应变和应力
        steel_strain = (d - c) / c * target_strain if c > 0 else 0

        # 钢筋应力（考虑拉压不同）
        if steel_strain > 0:  # 受拉
            steel_stress = min(Es * steel_strain, fy)
        else:  # 受压
            steel_stress = max(Es * steel_strain, -fy)

        # 钢筋合力
        A_steel = rho * np.pi * (D/2)**2
        Ts = steel_stress * A_steel

        # 力平衡方程：Cc - Ts - P = 0
        return Cc - Ts - P

    # 简化的二分法求解中性轴深度
    c_min = cover + 0.01  # 最小值
    c_max = D - cover - 0.01  # 最大值

    # 检查边界条件
    f_min = force_equilibrium(c_min)
    f_max = force_equilibrium(c_max)

    if f_min * f_max > 0:
        # 如果边界值同号，使用经验公式
        n = P / (np.pi * (D/2)**2 * fc) if P > 0 else 0
        c_solution = D/2 * (0.3 + 0.4*n + 0.1*rho)  # 考虑配筋率的经验公式
        c_solution = max(c_min, min(c_solution, c_max))
    else:
        # 二分法求解
        for _ in range(20):  # 最多迭代20次
            c_mid = (c_min + c_max) / 2
            f_mid = force_equilibrium(c_mid)

            if abs(f_mid) < 1000:  # 收敛判据：力平衡误差小于1kN
                c_solution = c_mid
                break

            if f_min * f_mid < 0:
                c_max = c_mid
                f_max = f_mid
            else:
                c_min = c_mid
                f_min = f_mid
        else:
            c_solution = (c_min + c_max) / 2

    return c_solution


def calculate_pier_params(params):
    """计算桥墩相关参数"""
    update_concrete_params(params)
    if params.num_spans == 1:
        return
    update_steel_params(params)
    calculate_design_params(params)

    # 预计算目标曲率
    params.pier_section['target_curvature_004'] = calculate_target_curvature(params, 0.004)
    params.pier_section['target_curvature_006'] = calculate_target_curvature(params, 0.006)

    return


def update_concrete_params(params):
    """更新混凝土参数"""
    for mat in params.concrete_materials.values():
        grade = mat["grade"]
        fcuk = int(grade[1:])  # 立方体抗压强度标准值 (MPa)
        fcu = 0.79 * fcuk      # 圆柱体抗压强度 (MPa)
        mat["Ec"] = 4700 * np.sqrt(fcu) # (MPa, 美国规范)
        mat["fc"] = fcu
        mat["fcuk"] = fcuk
        mat["ft"] = 0.79 * fcuk * 0.1  # (MPa)
        mat["Et"] = mat["gamma"] * mat["Ec"]  # (MPa)
    # 计算核心区混凝土极限应变
    calculate_ecu(params)
    return


def  update_steel_params(params):
    """更新钢筋参数"""
    adjust_longitudinal_reinforcement_ratio(params, target_ratio=0.015)
    return


def calculate_ecu(params):
    if params.num_spans == 1:
        rho_s = 0.004
    else:
        # 计算体积配箍率 约0.005
        transverse_params = params.pier_section['transverse_bars']
        # 箍筋参数
        transverse_dia = transverse_params["diameter"]     # 箍筋直径(m)
        transverse_spacing = transverse_params["spacing"]  # 箍筋间距(m)

        # 计算约束效应
        D = params.pier_section["diameter"]            # 桥墩直径(m)
        cover = params.pier_section["concrete_cover"]  # 保护层厚度(m)
        D_core = D - 2 * cover                         # 核心区直径(m)

        # 计算体积配箍率
        if transverse_params["configuration"] == "spiral":  # 螺旋箍筋
            rho_s = 4 * transverse_dia**2 / (D_core * transverse_spacing)
        else:  # 环形箍筋
            rho_s = 2 * transverse_dia**2 / (D_core * transverse_spacing)
        print(f"桥墩体积配箍率: {rho_s*100:.2f}%")
    params.pier_section['transverse_bars']['rho'] = rho_s
    assert rho_s >= 0.004

    fkh = params.steel_materials["transverse"]["fy"]
    fcck = 1.25 * params.concrete_materials["core"]["fc"]/0.79
    ecu = 0.004 + 1.4*rho_s*fkh*0.09/fcck
    print(f"约束混凝土极限应变: {ecu:.4f}")
    params.concrete_materials["core"]["ecu"] = ecu
    return ecu


def adjust_longitudinal_reinforcement_ratio(params, target_ratio=0.015):
    """
    调整桥墩纵向钢筋配筋率至目标值(默认1.5%)
    通过调整钢筋直径和数量来实现目标配筋率, 并更新params中的相关参数

    参数:
        params: 桥梁参数对象
        target_ratio: 目标配筋率, 默认为0.015

    返回:
        float: 调整后的实际配筋率
    """
    # 获取当前参数
    D = params.pier_section["diameter"]            # 桥墩直径(m)

    # 获取纵向钢筋参数
    long_bars = params.pier_section['longitudinal_bars']
    current_dia = long_bars['diameter']            # 当前钢筋直径(m)
    current_num = long_bars['number']              # 当前钢筋数量

    # 计算当前配筋率
    A_concrete = np.pi * (D/2)**2                  # 混凝土截面面积(m²)
    A_steel = current_num * np.pi * (current_dia/2)**2  # 钢筋总面积(m²)
    current_ratio = A_steel / A_concrete

    # print(f"当前桥墩纵向钢筋配筋率: {current_ratio*100:.2f}%")
    # print(f"目标桥墩纵向钢筋配筋率: {target_ratio*100:.2f}%")

    # 如果当前配筋率已经接近目标值，则不需要调整
    if abs(current_ratio - target_ratio) < 0.0005:  # 允许0.05%的误差
        # print("当前配筋率已接近目标值，无需调整")
        params.pier_section['longitudinal_bars']['rho'] = current_ratio
        print(f"桥墩纵向钢筋配筋率: {current_ratio*100:.2f}%")
        return current_ratio

    # 计算所需的钢筋总面积
    required_A_steel = A_concrete * target_ratio

    # 调整策略：优先调整钢筋直径，如果直径调整后仍不满足要求，则调整钢筋数量
    # 计算新的钢筋直径（保持数量不变）
    new_dia = np.sqrt(required_A_steel / (current_num * np.pi)) * 2

    # 检查新直径是否在合理范围内（12mm-40mm）
    if 0.012 <= new_dia <= 0.040:
        # 直径调整在合理范围内，使用新直径
        params.pier_section['longitudinal_bars']['diameter'] = new_dia
        # print(f"调整钢筋直径: {current_dia*1000:.1f}mm -> {new_dia*1000:.1f}mm (保持数量: {current_num}根)")
    else:
        # 直径超出合理范围，保持合理的直径并调整数量
        if new_dia < 0.012:
            new_dia = 0.012  # 最小直径12mm
        elif new_dia > 0.040:
            new_dia = 0.040  # 最大直径40mm

        # 计算所需的钢筋数量
        new_num = int(np.ceil(required_A_steel / (np.pi * (new_dia/2)**2)))

        # 确保钢筋数量是偶数（便于对称布置）
        if new_num % 2 != 0:
            new_num += 1

        params.pier_section['longitudinal_bars']['diameter'] = new_dia
        params.pier_section['longitudinal_bars']['number'] = new_num
        # print(f"调整钢筋: 直径 {current_dia*1000:.1f}mm -> {new_dia*1000:.1f}mm, 数量 {current_num}根 -> {new_num}根")

    # 计算调整后的实际配筋率
    new_dia = params.pier_section['longitudinal_bars']['diameter']
    new_num = params.pier_section['longitudinal_bars']['number']
    new_A_steel = new_num * np.pi * (new_dia/2)**2
    actual_ratio = new_A_steel / A_concrete

    print(f"桥墩纵向钢筋配筋率: {actual_ratio*100:.2f}%")

    # 更新params中的配筋率参数（如果需要）
    params.pier_section['longitudinal_bars']['rho'] = actual_ratio

    return actual_ratio


def calculate_design_params(params):
    """计算桥墩设计参数"""
    fck = params.concrete_materials["core"]["fcuk"] * 1e6  # (N/m²)
    Ag = np.pi * (params.pier_section['diameter']/2)**2  # (m²)
    ecu = params.concrete_materials["core"]["ecu"]

    # 计算单根柱截面实际所受轴力P
    num_piers = (params.num_spans-1) * params.num_piers_transverse
    weight = params.super_weight
    weight /= params.num_spans
    weight += params.cap_weight / (params.num_spans-1)
    weight += params.pier_weight / num_piers
    P = weight / params.num_piers_transverse  # N
    params.pier_section['axial_force'] = P
    
    # 计算轴压比etak
    etak = P / Ag / (params.concrete_materials["core"]["fc"]*1e6)
    params.pier_section['etak'] = etak
    print(f"桥墩轴压比: {etak:.4f}")
    
    # 塑性铰区域内加密箍筋的最小体积配箍率rhos_min
    rhot = params.pier_section['longitudinal_bars']['rho']
    fhk = params.steel_materials["transverse"]["fu"]*1e6
    rhos_min = (0.14*etak + 5.84*(etak-0.1)*(rhot-0.01) + 0.028) * fck / fhk
    if rhos_min < 0.004:
        print("加密箍筋最低体积配箍率不足0.004, 重新设计...")
        if params.pier_section['diameter'] < 0.5:
            print("桥墩直径过小！")
        else:
            params.pier_section['diameter'] -= 0.05
            calculate_design_params(params)
    params.pier_section['rhos_min'] = rhos_min
    print(f"桥墩加密箍筋最小体积配箍率: {rhos_min:.4f}")

    # 计算极限曲率系数phyu (根据规范公式, 1/m)
    D = params.pier_section['diameter']  # 桥墩直径 (m)
    es = params.steel_materials["longitudinal"]["eu"]
    phiu1 = (2.826e-3 + 6.85*ecu - (8.575e-3 + 18.638*ecu) * P/(fck*Ag)) / D
    phiu2 = (1.635e-3 + 1.179*es + (28.739*es*es + 0.656*es + 0.01) * P/(fck*Ag)) / D
    phiu = np.minimum(phiu1, phiu2)
    print('桥墩极限曲率：{:.6f} (1/m)'.format(phiu))
    params.pier_section['phiu'] = phiu
    return phiu