"""
Simply Supported Bridge Boundary Conditions Module

This module handles the application of boundary conditions to bridge components.
"""

import openseespy.opensees as ops


def apply_boundary_conditions(model):
    """Apply boundary conditions to the bridge model"""
    # Pier bottom nodes
    if model.piers['nodes']:
        for pier_nodes in model.piers['nodes'].values():
            bot_node = pier_nodes[0]  # Bottom node is first in list
            if not model.params.pile_soil["enable_ssi"]:
                ops.fix(bot_node, 1, 1, 1, 1, 1, 1)  # Fix all DOFs
            else:
                ops.fix(bot_node, 0, 1, 1, 1, 1, 1)  # Fix all DOFs except X
                
                pile_node = model._next_tag('node')
                ops.node(pile_node, *ops.nodeCoord(bot_node))
                ops.fix(pile_node, 1, 1, 1, 1, 1, 1)  # Fix all DOFs
                pile_mat_tag = model.mat_tags['PilePierColumn']
                
                elem_tag = model._next_tag('element')
                ops.element('zeroLength', elem_tag, bot_node, pile_node,
                            '-mat', pile_mat_tag,
                            '-dir', 1)
                model.piers['elements'].append(elem_tag)

    # Apply abutment boundary conditions
    apply_abutment_boundary_conditions(model)


def apply_abutment_boundary_conditions(model):
    """Apply boundary conditions at abutments with backfill springs"""
    assert len(model.abutments['nodes']) == 2

    left_abutment = model.abutments['nodes'][0]
    if not model.params.pile_soil["enable_ssi"]:
        ops.fix(left_abutment, 1, 1, 1, 1, 1, 1)  # Fix all DOFs
    else: 
        ops.fix(left_abutment, 0, 1, 1, 1, 1, 1)
    create_abutment_boundary(model, left_abutment, 'left')

    right_abutment = model.abutments['nodes'][-1]
    if not model.params.pile_soil["enable_ssi"]:
        ops.fix(right_abutment, 1, 1, 1, 1, 1, 1)  # Fix all DOFs
    else: 
        ops.fix(right_abutment, 0, 1, 1, 1, 1, 1)
    create_abutment_boundary(model, right_abutment, 'right')


def create_abutment_boundary(model, abutment_node, abutment_side):
    # Create a fixed node at the abutment location
    coords = ops.nodeCoord(abutment_node)
    soil_node = model._next_tag('node')
    ops.node(soil_node, *coords)
    ops.fix(soil_node, 1, 1, 1, 1, 1, 1)  # Fix all DOFs

    # Direction of backfill spring depends on abutment side
    if abutment_side == 'left':
        backfill_mat_tag = model.mat_tags['BackfillLeft']
    else:  # abutment_side == 'right'
        backfill_mat_tag = model.mat_tags['BackfillRight']

    # Create the zero-length element with constraints in all directions
    # Use appropriate backfill material for longitudinal direction (X)
    elem_tag = model._next_tag('element')
    ops.element('zeroLength', elem_tag, abutment_node, soil_node,
                '-mat', backfill_mat_tag,
                '-dir', 1)
    model.abutments['elements'].append(elem_tag)
    
    if model.params.pile_soil["enable_ssi"]:
        pile_node = model._next_tag('node')
        ops.node(pile_node, *coords)
        ops.fix(pile_node, 1, 1, 1, 1, 1, 1)  # Fix all DOFs
        pile_mat_tag = model.mat_tags['PileAbutment']
        
        elem_tag = model._next_tag('element')
        ops.element('zeroLength', elem_tag, abutment_node, pile_node,
                    '-mat', pile_mat_tag,
                    '-dir', 1)
        model.abutments['elements'].append(elem_tag)

